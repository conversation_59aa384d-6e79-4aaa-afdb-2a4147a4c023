version: '3.8'

services:
  # MongoDB service
  mongodb:
    image: mongo:7.0
    container_name: medicare_mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: mongo
      MONGO_INITDB_ROOT_PASSWORD: mysecretpassword
      MONGO_INITDB_DATABASE: medicare
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
    networks:
      - medicare_network

  # Redis service
  redis:
    image: redis:7.2-alpine
    container_name: medicare_redis
    restart: unless-stopped
    command: redis-server --requirepass mysecretpassword
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - medicare_network

  # Medicare Go application
  medicare_app:
    build: .
    container_name: medicare_app
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - DATABASE_URL=************************************************************************
      - DBNAME=medicare
      - REDIS_URL=redis://:mysecretpassword@redis:6379/0
      - PORT=8081
      - GIN_MODE=release
    depends_on:
      - mongodb
      - redis
    networks:
      - medicare_network
    volumes:
      - ./logs:/app/logs  # Optional: for application logs

# Named volumes for data persistence
volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

# Custom network
networks:
  medicare_network:
    driver: bridge
