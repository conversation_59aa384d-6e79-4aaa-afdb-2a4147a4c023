package main

import (
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/platinumpizza29/medicare/internal/db"
)

func main() {
	// Load environment variables from .env file
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: Error loading .env file: %v", err)
	}

	router := gin.Default()

	mongoUri := os.Getenv("DATABASE_URL")
	dbName := os.Getenv("DBNAME")
	redisUri := os.Getenv("REDIS_URL")

	if err := db.ConnectMongo(mongoUri, dbName); err != nil {
		log.Fatal(err)

	}

	if err := db.ConnectRedis(redisUri); err != nil {
		log.Fatal(err)
	}

	log.Fatal(router.Run(":8080"))
}
