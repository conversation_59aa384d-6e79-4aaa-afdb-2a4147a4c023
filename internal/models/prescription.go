package models

type Prescription struct {
	ID        string     `bson:"_id,omitempty" json:"id"`
	VisitID   string     `bson:"visitId" json:"visitId"`
	DoctorID  string     `bson:"doctorId" json:"doctorId"`
	PatientID string     `bson:"patientId" json:"patientId"`
	Medicines []Medicine `bson:"medicines" json:"medicines"`
	CreatedAt int64      `bson:"createdAt" json:"createdAt"`
}

type Medicine struct {
	Name         string `bson:"name" json:"name"`
	Dosage       string `bson:"dosage" json:"dosage"`
	Frequency    string `bson:"frequency" json:"frequency"`
	Duration     string `bson:"duration" json:"duration"`
	Instructions string `bson:"instructions" json:"instructions"`
}
