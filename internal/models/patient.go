package models

import "time"

type Patient struct {
	ID          string    `bson:"_id,omitempty" json:"id"`
	Name        string    `bson:"name" json:"name"`
	Phone       string    `bson:"phone" json:"phone"`
	DateOfBirth string    `bson:"dateOfBirth" json:"dateOfBirth"`
	Gender      string    `bson:"gender" json:"gender"`
	ClinicID    string    `bson:"clinicId" json:"clinicId"`
	CreatedAt   time.Time `bson:"createdAt" json:"createdAt"`
	UpdatedAt   time.Time `bson:"updatedAt" json:"updatedAt"`
}
