package models

type Patient struct {
	ID          string `bson:"_id,omitempty" json:"id"`
	Name        string `bson:"name" json:"name"`
	Phone       string `bson:"phone" json:"phone"`
	DateOfBirth string `bson:"dateOfBirth" json:"dateOfBirth"`
	Gender      string `bson:"gender" json:"gender"`
	ClinicID    string `bson:"clinicId" json:"clinicId"`
	CreatedAt   int64  `bson:"createdAt" json:"createdAt"`
	UpdatedAt   int64  `bson:"updatedAt" json:"updatedAt"`
}
