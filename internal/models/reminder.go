package models

type Reminder struct {
	ID             string `bson:"_id,omitempty" json:"id"`
	PrescriptionID string `bson:"prescriptionId" json:"prescriptionId"`
	PatientID      string `bson:"patientId" json:"patientId"`
	MedicineName   string `bson:"medicineName" json:"medicineName"`
	ScheduledAt    int64  `bson:"scheduledAt" json:"scheduledAt"`
	Status         string `bson:"status" json:"status"` // taken, skipped, pending
}
