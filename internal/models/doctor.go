package models

import "time"

type Doctor struct {
	ID        string    `bson:"_id,omitempty" json:"id"`
	Name      string    `bson:"name" json:"name"`
	Phone     string    `bson:"phone" json:"phone"`
	Specialty string    `bson:"specialty" json:"specialty"`
	ClinicID  string    `bson:"clinicId" json:"clinicId"`
	CreatedAt time.Time `bson:"createdAt" json:"createdAt"`
	UpdatedAt time.Time `bson:"updatedAt" json:"updatedAt"`
}
