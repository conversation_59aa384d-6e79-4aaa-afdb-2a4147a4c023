package services

import (
	"context"
	"fmt"

	"github.com/platinumpizza29/medicare/internal/db"
	"github.com/platinumpizza29/medicare/internal/models"
	"github.com/platinumpizza29/medicare/internal/utils"
)

type DoctorService struct {
	doctorModel *db.DoctorModel
}

func NewDoctorService(doctorDB *db.DoctorModel) *DoctorService {
	return &DoctorService{
		doctorModel: doctorDB,
	}
}

func (s *DoctorService) LoginDoctor(ctx context.Context, phone string, password string) (*models.Doctor, error) {
	// check if the password is correct

	// call the db
	cursor, err := s.doctorModel.GetDoctorByPhone(ctx, phone)
	if err != nil {
		return nil, err
	}

	isValid := utils.CheckPasswordHash(password, cursor.Password)
	if !isValid {
		return nil, fmt.Errorf("invalid credentials")
	}

	return cursor, nil
}

// register doctor
func (s *DoctorService) RegisterDoctor(ctx context.Context, doctor *models.Doctor) error {
	// encrypt the password with bcrypt
	password, err := utils.HashPassword(doctor.Password)
	if err != nil {
		return err
	}
	doctor.Password = password

	_, err = s.doctorModel.CreateDoctor(ctx, doctor, db.Database)
	if err != nil {
		return err
	}
	return nil
}
