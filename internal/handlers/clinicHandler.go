package handlers

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/platinumpizza29/medicare/internal/models"
	"github.com/platinumpizza29/medicare/internal/services"
)

type ClinicHandler struct {
	clinicService *services.ClinicService
}

func NewClinicHandler(clinicService *services.ClinicService) *ClinicHandler {
	return &ClinicHandler{
		clinicService: clinicService,
	}
}

func (s *ClinicHandler) LoginClinic(c *gin.Context) {
	var clinic *models.Clinic

	if err := c.ShouldBindJSON(&clinic); err != nil {
		c.JSON(400, gin.H{"error": err.<PERSON><PERSON>r()})
		return
	}
}

func (s *ClinicHandler) RegisterClinic(c *gin.Context) {
	var clinic *models.Clinic
	var ctx context.Context

	if err := c.Should<PERSON>ind<PERSON>(&clinic); err != nil {
		c.JSON(400, gin.H{"error": err.<PERSON><PERSON><PERSON>()})
		return
	}

	if err := s.clinicService.CreateClinic(ctx, clinic); err != nil {
		c.<PERSON>(500, gin.H{"error": err.<PERSON>rror()})
		return
	}

	c.<PERSON>(200, gin.H{"message": "Clinic created successfully"})
}
