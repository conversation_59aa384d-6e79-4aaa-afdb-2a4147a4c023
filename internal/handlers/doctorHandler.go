package handlers

import (
	"github.com/gin-gonic/gin"
	"github.com/platinumpizza29/medicare/internal/models"
	"github.com/platinumpizza29/medicare/internal/services"
)

type DoctorHandler struct {
	doctorService *services.DoctorService
}

func NewDoctorHandler(doctorService *services.DoctorService) *Doctor<PERSON><PERSON>ler {
	return &DoctorHandler{
		doctorService: doctorService,
	}
}

type LoginRequest struct {
	Phone    string `json:"phone"`
	Password string `json:"password"`
}

func (s *<PERSON><PERSON>and<PERSON>) LoginDoctor(c *gin.Context) {
	var doctor *models.Doctor

	if err := c.ShouldBindJSON(&doctor); err != nil {
		c.IndentedJSON(400, gin.H{"error": err.<PERSON>rror()})
		return
	}
	// call the service
	ctx := c.Request.Context()
	cursor, err := s.doctorService.LoginDoctor(ctx, doctor.Phone, doctor.Password)
	if err != nil {
		c.IndentedJSON(500, gin.H{"error": err.<PERSON>rror()})
		return
	}

	c.IndentedJSON(200, gin.H{"doctor": cursor})
}

func (s *<PERSON><PERSON>and<PERSON>) RegisterDoctor(c *gin.Context) {
	var doctor *models.Doctor

	if err := c.ShouldBindJSON(&doctor); err != nil {
		c.IndentedJSON(400, gin.H{"error": err.Error()})
		return
	}
	// call the service
	ctx := c.Request.Context()
	if err := s.doctorService.RegisterDoctor(ctx, doctor); err != nil {
		c.IndentedJSON(500, gin.H{"error": err.Error()})
		return
	}

	c.IndentedJSON(200, gin.H{"message": "Doctor created successfully"})
}
